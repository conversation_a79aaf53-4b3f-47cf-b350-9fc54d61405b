import { auth } from "@/lib/auth";
import { WECOM_CORP_ID, WECOM_CORP_SECRET, WECOM_AGENT_ID } from "astro:env/server";
import type { APIRoute } from "astro";

export const ALL: APIRoute = async (ctx) => {
  // 特殊处理企业微信的回调
  if (ctx.request.url.includes("/api/auth/oauth2/callback/wecom")) {
    const url = new URL(ctx.request.url);
    const code = url.searchParams.get("code");
    const state = url.searchParams.get("state");

    if (code) {
      try {
        // 1. 获取企业微信应用的 access_token
        const tokenResponse = await fetch(
          `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${WECOM_CORP_ID}&corpsecret=${WECOM_CORP_SECRET}`
        );
        const tokenData = await tokenResponse.json();

        if (tokenData.errcode !== 0) {
          console.error('获取企业微信 access_token 失败:', tokenData);
          return new Response('认证失败', { status: 400 });
        }

        // 2. 使用 code 和 access_token 获取用户信息
        const userInfoResponse = await fetch(
          `https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=${tokenData.access_token}&code=${code}`
        );
        const userInfo = await userInfoResponse.json();

        if (userInfo.errcode !== 0) {
          console.error('获取企业微信用户信息失败:', userInfo);
          return new Response('获取用户信息失败', { status: 400 });
        }

        console.log('企业微信用户信息:', userInfo);

        // 3. 如果需要更详细的用户信息，可以继续调用用户详情接口
        if (userInfo.userid) {
          const userDetailResponse = await fetch(
            `https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=${tokenData.access_token}&userid=${userInfo.userid}`
          );
          const userDetail = await userDetailResponse.json();
          console.log('企业微信用户详情:', userDetail);
        }

        // 4. 构造一个模拟的 OAuth2 响应，让 better-auth 能够处理
        const modifiedUrl = new URL(ctx.request.url);
        // 将企业微信的用户信息存储在某个地方，供 getUserInfo 函数使用
        // 这里我们可以使用 state 参数或者其他方式传递信息

      } catch (error) {
        console.error('企业微信认证处理失败:', error);
        return new Response('认证处理失败', { status: 500 });
      }
    }
  }

  return auth.handler(ctx.request);
};