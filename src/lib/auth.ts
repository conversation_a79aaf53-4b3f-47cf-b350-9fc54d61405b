import { WECOM_AGENT_ID, WECOM_CORP_ID } from "astro:env/client";
import { WECOM_CORP_SECRET } from "astro:env/server";
import { betterAuth } from "better-auth";
import { genericOAuth } from "better-auth/plugins";

export const auth = betterAuth({
  trustedOrigins: ["https://paykka-duty.liyujun.dev", "http://localhost:4321"],
  telemetry: { enabled: false },
  plugins: [
    genericOAuth({
      config: [
        {
          providerId: "wecom",
          clientId: WECOM_CORP_ID,
          clientSecret: WECOM_CORP_SECRET,
          authorizationUrl: "https://open.weixin.qq.com/connect/oauth2/authorize#wechat_redirect",
          authorizationUrlParams: {
            appid: WECOM_CORP_ID,
            agentid: WECOM_AGENT_ID,
          },
          tokenUrl: "https://qyapi.weixin.qq.com/cgi-bin/gettoken",
          tokenUrlParams: {
            corpid: WECOM_CORP_ID,
            corpsecret: WECOM_CORP_SECRET
          },
          userInfoUrl: "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo",
          scopes: ["snsapi_privateinfo"],
          getUserInfo: async (token) => {
            console.log(111111)
            console.log(token)
            return {
              id: "liyujun",
              name: "liyujun",
              email: "<EMAIL>",
              emailVerified: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            }
          }
        }
      ]
    })
  ]
})