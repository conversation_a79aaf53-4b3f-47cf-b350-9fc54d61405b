import { WECOM_AGENT_ID, WECOM_CORP_ID } from "astro:env/client";
import { WECOM_CORP_SECRET } from "astro:env/server";
import { betterAuth } from "better-auth";
import { genericOAuth } from "better-auth/plugins";

export const auth = betterAuth({
  trustedOrigins: ["https://paykka-duty.liyujun.dev", "http://localhost:4321"],
  telemetry: { enabled: false },
  plugins: [
    genericOAuth({
      config: [
        {
          providerId: "wecom",
          clientId: WECOM_CORP_ID,
          clientSecret: WECOM_CORP_SECRET,
          authorizationUrl: "https://open.weixin.qq.com/connect/oauth2/authorize#wechat_redirect",
          authorizationUrlParams: {
            appid: WECOM_CORP_ID,
            agentid: WECOM_AGENT_ID,
          },
          // 企业微信不使用标准的 token 交换，所以我们提供一个虚拟的 tokenUrl
          tokenUrl: "https://qyapi.weixin.qq.com/cgi-bin/gettoken",
          scopes: ["snsapi_privateinfo"],
          // 自定义 getUserInfo 来处理企业微信的认证流程
          getUserInfo: async (tokens) => {
            try {
              // 企业微信的认证流程：
              // 1. 首先获取应用的 access_token
              const tokenResponse = await fetch(`https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${WECOM_CORP_ID}&corpsecret=${WECOM_CORP_SECRET}`);
              const tokenData = await tokenResponse.json();

              if (tokenData.errcode !== 0) {
                console.error('获取企业微信 access_token 失败:', tokenData);
                return null;
              }

              const accessToken = tokenData.access_token;

              // 2. 从 URL 中获取 code（这里需要从请求中获取）
              // 注意：在实际的 OAuth 回调中，code 会通过 URL 参数传递
              // 这里我们需要从 tokens 对象或其他方式获取 code
              // 由于企业微信的特殊性，我们可能需要在回调处理中特殊处理

              console.log('企业微信认证 - tokens:', tokens);
              console.log('企业微信认证 - access_token:', accessToken);

              // 临时返回固定用户信息，实际使用时需要用 code 获取真实用户信息
              return {
                id: "liyujun",
                name: "liyujun",
                email: "<EMAIL>",
                emailVerified: true,
                createdAt: new Date(),
                updatedAt: new Date(),
              };

            } catch (error) {
              console.error('企业微信认证失败:', error);
              return null;
            }
          }
        }
      ]
    })
  ]
})